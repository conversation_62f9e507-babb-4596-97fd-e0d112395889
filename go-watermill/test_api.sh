#!/bin/bash

# Test script for go-watermill API

PORT=${HTTP_PORT:-8090}
BASE_URL="http://localhost:$PORT"

echo "Testing go-watermill API on $BASE_URL"
echo "=================================="

# Test health endpoint
echo "1. Testing health endpoint..."
curl -s "$BASE_URL/health" | jq .
echo ""

# Test info endpoint
echo "2. Testing info endpoint..."
curl -s "$BASE_URL/" | jq .
echo ""

# Test publish endpoint
echo "3. Testing publish endpoint..."
curl -s -X POST "$BASE_URL/api/v1/publish" \
  -H "Content-Type: application/json" \
  -d '{"topic": "test.topic", "message": "Hello from test script"}' | jq .
echo ""

# Test events endpoint
echo "4. Testing events endpoint..."
curl -s -X POST "$BASE_URL/api/v1/events" \
  -H "Content-Type: application/json" \
  -d '{"topic": "user.action", "message": "User performed test action"}' | jq .
echo ""

# Test multiple messages
echo "5. Testing multiple messages..."
for i in {1..3}; do
  echo "Sending message $i..."
  curl -s -X POST "$BASE_URL/api/v1/publish" \
    -H "Content-Type: application/json" \
    -d "{\"topic\": \"batch.test\", \"message\": \"Batch message $i\"}" | jq .
done

echo ""
echo "API testing completed!"
