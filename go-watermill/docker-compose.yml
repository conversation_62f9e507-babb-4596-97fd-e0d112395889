version: '3.8'

services:
  app:
    build: .
    environment:
      - APP_NAME=go-watermill
      - APP_VERSION=1.0.0
      - DEBUG=true
      - HTTP_PORT=8080
      - WATERMILL_ASYNC=true
      - WATERMILL_MAX_PENDING=1000
      - WATERMILL_CONCURRENT_HANDLERS=10
      - WATERMILL_ACK_WAIT=30s
    ports:
      - "8080:8080"
    # healthcheck:
    #   test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 3
    #   start_period: 30s
    # restart: unless-stopped

  # Optional: Add a second instance for testing load balancing
  app2:
    build: .
    environment:
      - APP_NAME=go-watermill
      - APP_VERSION=1.0.0
      - DEBUG=true
      - HTTP_PORT=8080
      - WATERMILL_ASYNC=true
      - WATERMILL_MAX_PENDING=1000
      - WATERMILL_CONCURRENT_HANDLERS=10
      - WATERMILL_ACK_WAIT=30s
    ports:
      - "8081:8080"
    # healthcheck:
    #   test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 3
    #   start_period: 30s
    # restart: unless-stopped

networks:
  default:
    driver: bridge
