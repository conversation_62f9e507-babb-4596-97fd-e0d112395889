package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go-watermill/internal/config"
	"go-watermill/internal/watermill"
	"go-watermill/pkg/api"
)

func main() {
	log.Println("Starting go-watermill application...")

	// Load configuration
	cfg := config.Load()
	log.Printf("Loaded configuration: app=%s, version=%s, debug=%v",
		cfg.App.Name, cfg.App.Version, cfg.App.Debug)

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create Watermill service
	watermillSvc := watermill.NewWatermillService(cfg)
	watermillService := watermillSvc.(*watermill.WatermillService)

	// Start Watermill service
	if err := watermillService.Start(ctx); err != nil {
		log.Fatalf("Failed to start Watermill service: %v", err)
	}
	defer func() {
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()
		if err := watermillService.Stop(shutdownCtx); err != nil {
			log.Printf("Error stopping Watermill service: %v", err)
		}
	}()

	// Wait for Watermill service to be ready
	<-watermillService.Running()
	log.Println("Watermill service is ready")

	// Create HTTP server with CQRS publisher
	cqrsPublisher := watermillService.GetCQRSPublisher()
	httpServer := api.NewHTTPServer(cfg, cqrsPublisher)

	// Start HTTP server
	if err := httpServer.Start(ctx); err != nil {
		log.Fatalf("Failed to start HTTP server: %v", err)
	}
	defer func() {
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()
		if err := httpServer.Stop(shutdownCtx); err != nil {
			log.Printf("Error stopping HTTP server: %v", err)
		}
	}()

	// Wait for HTTP server to be ready
	<-httpServer.Running()
	log.Println("HTTP server is ready")

	log.Printf("Application started successfully!")
	log.Printf("- HTTP API: http://localhost:%d", cfg.HTTP.Port)
	log.Printf("- Health check: http://localhost:%d/health", cfg.HTTP.Port)
	log.Printf("- Publish endpoint: POST http://localhost:%d/api/v1/publish", cfg.HTTP.Port)
	log.Printf("- Events endpoint: POST http://localhost:%d/api/v1/events", cfg.HTTP.Port)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	log.Println("Shutting down application...")
	cancel() // Cancel the context to signal shutdown
}
