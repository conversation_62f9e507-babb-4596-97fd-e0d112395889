package watermill

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"go-watermill/internal/config"
	"go-watermill/internal/cqrs"
	"go-watermill/internal/service"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/pubsub/gochannel"
)

// WatermillService implements the Service interface for Watermill
type WatermillService struct {
	config      *config.Config
	publisher   message.Publisher
	subscriber  message.Subscriber
	router      *message.Router
	logger      watermill.LoggerAdapter
	cqrsService *cqrs.CQRSService
	running     chan struct{}
	isRunning   bool
	mu          sync.RWMutex
}

// NewWatermillService creates a new Watermill service with Go Channel PubSub
func NewWatermillService(cfg *config.Config) service.Service {
	logger := watermill.NewStdLogger(cfg.App.Debug, cfg.App.Debug)

	// Create Go Channel PubSub
	pubSub := gochannel.NewGoChannel(
		gochannel.Config{
			OutputChannelBuffer:            1000,
			Persistent:                     false,
			BlockPublishUntilSubscriberAck: false,
		},
		logger,
	)

	watermillService := &WatermillService{
		config:     cfg,
		publisher:  pubSub,
		subscriber: pubSub,
		logger:     logger,
		running:    make(chan struct{}),
		isRunning:  false,
	}

	// CQRS service will be created after router is ready

	return watermillService
}

// Start starts the Watermill service
func (w *WatermillService) Start(ctx context.Context) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.isRunning {
		return fmt.Errorf("watermill service is already running")
	}

	// Create and configure router
	if err := w.createRouter(); err != nil {
		return fmt.Errorf("failed to create router: %w", err)
	}

	// Create CQRS service after router is created
	cqrsService, err := cqrs.NewCQRSService(w.publisher, w.subscriber, w.router)
	if err != nil {
		return fmt.Errorf("failed to create CQRS service: %w", err)
	}
	w.cqrsService = cqrsService

	// Setup additional CQRS event subscriptions
	w.cqrsService.SetupEventSubscriptions()

	// Start router in background
	go func() {
		if err := w.router.Run(ctx); err != nil {
			log.Printf("Router error: %v", err)
		}
	}()

	// Wait for router to be ready
	<-w.router.Running()

	w.isRunning = true
	close(w.running)

	log.Println("Watermill service started successfully")
	return nil
}

// Stop implements graceful shutdown of the Watermill service
func (w *WatermillService) Stop(ctx context.Context) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.isRunning {
		return nil
	}

	var errors []error

	// Close router
	if w.router != nil {
		if err := w.router.Close(); err != nil {
			errors = append(errors, fmt.Errorf("error closing router: %w", err))
		}
	}

	// Close publisher
	if w.publisher != nil {
		if err := w.publisher.Close(); err != nil {
			errors = append(errors, fmt.Errorf("error closing publisher: %w", err))
		}
	}

	// Close subscriber
	if w.subscriber != nil {
		if err := w.subscriber.Close(); err != nil {
			errors = append(errors, fmt.Errorf("error closing subscriber: %w", err))
		}
	}

	w.isRunning = false
	log.Println("Watermill service stopped")

	if len(errors) > 0 {
		return fmt.Errorf("errors during shutdown: %v", errors)
	}

	return nil
}

// Running returns a channel that is closed when the service is ready
func (w *WatermillService) Running() chan struct{} {
	return w.running
}

// IsRunning returns true if the service is currently running
func (w *WatermillService) IsRunning() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.isRunning
}

// GetPublisher returns the message publisher
func (w *WatermillService) GetPublisher() message.Publisher {
	return w.publisher
}

// GetSubscriber returns the message subscriber
func (w *WatermillService) GetSubscriber() message.Subscriber {
	return w.subscriber
}

// PublishMessage publishes a message to the specified topic
func (w *WatermillService) PublishMessage(topic string, payload []byte) error {
	msg := message.NewMessage(watermill.NewUUID(), payload)
	msg.Metadata.Set("timestamp", time.Now().Format(time.RFC3339))

	return w.publisher.Publish(topic, msg)
}

// GetCQRSPublisher returns a CQRS publisher that implements service.Publisher
func (w *WatermillService) GetCQRSPublisher() service.Publisher {
	return cqrs.NewCQRSPublisher(w.cqrsService)
}

// createRouter creates and configures the message router
func (w *WatermillService) createRouter() error {
	w.router, _ = message.NewRouter(message.RouterConfig{}, w.logger)

	// Add example message handler
	w.router.AddHandler(
		"example_handler",
		"example.topic",
		w.subscriber,
		"example.processed",
		w.publisher,
		w.handleExampleMessage,
	)

	// Add no-publisher handler for events that don't need to publish responses
	w.router.AddNoPublisherHandler(
		"event_handler",
		"events.published",
		w.subscriber,
		w.handlePublishedEvent,
	)

	// Setup CQRS event subscriptions (will be called after CQRS service is created)

	return nil
}

// handleExampleMessage handles example messages
func (w *WatermillService) handleExampleMessage(msg *message.Message) ([]*message.Message, error) {
	log.Printf("Received example message: %s", string(msg.Payload))

	// Create response message
	response := message.NewMessage(watermill.NewUUID(), []byte(fmt.Sprintf("Processed: %s", string(msg.Payload))))
	response.Metadata.Set("original_id", msg.UUID)
	response.Metadata.Set("processed_at", time.Now().Format(time.RFC3339))

	return []*message.Message{response}, nil
}

// handlePublishedEvent handles published events
func (w *WatermillService) handlePublishedEvent(msg *message.Message) error {
	log.Printf("Received published event: %s", string(msg.Payload))

	// Process the event (e.g., update database, send notifications, etc.)
	timestamp := msg.Metadata.Get("timestamp")
	log.Printf("Event timestamp: %s", timestamp)

	return nil
}
