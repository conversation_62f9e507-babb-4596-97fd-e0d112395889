package config

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	App       AppConfig       `json:"app"`
	HTTP      HTTPConfig      `json:"http"`
	Watermill WatermillConfig `json:"watermill"`
}

type AppConfig struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Debug   bool   `json:"debug"`
}

type HTTPConfig struct {
	Port int `json:"port"`
}

type WatermillConfig struct {
	Publisher  PublisherConfig  `json:"publisher"`
	Subscriber SubscriberConfig `json:"subscriber"`
}

type PublisherConfig struct {
	Async      bool `json:"async"`
	MaxPending int  `json:"max_pending"`
}

type SubscriberConfig struct {
	ConcurrentHandlers int           `json:"concurrent_handlers"`
	AckWait            time.Duration `json:"ack_wait"`
}

func Load() *Config {
	return &Config{
		App: AppConfig{
			Name:    getEnv("APP_NAME", "go-watermill"),
			Version: getEnv("APP_VERSION", "1.0.0"),
			Debug:   getEnvBool("DEBUG", false),
		},
		HTTP: HTTPConfig{
			Port: getEnvInt("HTTP_PORT", 8080),
		},
		Watermill: WatermillConfig{
			Publisher: PublisherConfig{
				Async:      getEnvBool("WATERMILL_ASYNC", true),
				MaxPending: getEnvInt("WATERMILL_MAX_PENDING", 1000),
			},
			Subscriber: SubscriberConfig{
				ConcurrentHandlers: getEnvInt("WATERMILL_CONCURRENT_HANDLERS", 10),
				AckWait:            getEnvDuration("WATERMILL_ACK_WAIT", 30*time.Second),
			},
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
