package service

import "context"

// Service defines the interface that all services must implement
type Service interface {
	// Start starts the service
	Start(ctx context.Context) error
	// Stop implements graceful shutdown of the service implementation
	Stop(ctx context.Context) error
	// Running sends signal when service is ready to process requests
	Running() chan struct{}
	// IsRunning returns true if the service is currently running
	IsRunning() bool
}

// Publisher defines the interface for publishing messages
type Publisher interface {
	// PublishMessage publishes a message to the specified topic
	PublishMessage(topic string, payload []byte) error
}
