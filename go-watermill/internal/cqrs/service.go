package cqrs

import (
	"context"
	"fmt"
	"log"

	"go-watermill/internal/service"

	"github.com/ThreeDotsLabs/watermill/components/cqrs"
	"github.com/ThreeDotsLabs/watermill/message"
)

// CQRSService integrates CQRS with Watermill using built-in components
type CQRSService struct {
	publisher        message.Publisher
	subscriber       message.Subscriber
	router           *message.Router
	commandBus       *cqrs.CommandBus
	eventBus         *cqrs.EventBus
	commandProcessor *cqrs.CommandProcessor
	eventProcessor   *cqrs.EventProcessor
}

// NewCQRSService creates a new CQRS service using Watermill CQRS components
func NewCQRSService(publisher message.Publisher, subscriber message.Subscriber, router *message.Router) (*CQRSService, error) {
	// Create marshaler for commands and events
	marshaler := cqrs.JSONMarshaler{}

	// Create command bus
	commandBus, err := cqrs.NewCommandBusWithConfig(publisher, cqrs.CommandBusConfig{
		GeneratePublishTopic: func(params cqrs.CommandBusGeneratePublishTopicParams) (string, error) {
			return fmt.Sprintf("commands.%s", params.CommandName), nil
		},
		Marshaler: marshaler,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create command bus: %w", err)
	}

	// Create event bus
	eventBus, err := cqrs.NewEventBusWithConfig(publisher, cqrs.EventBusConfig{
		GeneratePublishTopic: func(params cqrs.GenerateEventPublishTopicParams) (string, error) {
			return fmt.Sprintf("events.%s", params.EventName), nil
		},
		Marshaler: marshaler,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create event bus: %w", err)
	}

	// Create command processor
	commandProcessor, err := cqrs.NewCommandProcessorWithConfig(router, cqrs.CommandProcessorConfig{
		GenerateSubscribeTopic: func(params cqrs.CommandProcessorGenerateSubscribeTopicParams) (string, error) {
			return fmt.Sprintf("commands.%s", params.CommandName), nil
		},
		SubscriberConstructor: func(params cqrs.CommandProcessorSubscriberConstructorParams) (message.Subscriber, error) {
			return subscriber, nil
		},
		Marshaler: marshaler,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create command processor: %w", err)
	}

	// Create event processor
	eventProcessor, err := cqrs.NewEventProcessorWithConfig(router, cqrs.EventProcessorConfig{
		GenerateSubscribeTopic: func(params cqrs.EventProcessorGenerateSubscribeTopicParams) (string, error) {
			return fmt.Sprintf("events.%s", params.EventName), nil
		},
		SubscriberConstructor: func(params cqrs.EventProcessorSubscriberConstructorParams) (message.Subscriber, error) {
			return subscriber, nil
		},
		Marshaler: marshaler,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create event processor: %w", err)
	}

	cqrsService := &CQRSService{
		publisher:        publisher,
		subscriber:       subscriber,
		router:           router,
		commandBus:       commandBus,
		eventBus:         eventBus,
		commandProcessor: commandProcessor,
		eventProcessor:   eventProcessor,
	}

	// Register command handlers
	publishMessageHandler := NewPublishMessageCommandHandler(eventBus)
	err = commandProcessor.AddHandlers(
		cqrs.NewCommandHandler("PublishMessageCommandHandler", publishMessageHandler.Handle),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to register command handlers: %w", err)
	}

	// Register event handlers
	messagePublishedHandler := NewMessagePublishedEventHandler()
	userActionHandler := NewUserActionEventHandler()

	err = eventProcessor.AddHandlers(
		cqrs.NewEventHandler("MessagePublishedEventHandler", messagePublishedHandler.Handle),
		cqrs.NewEventHandler("UserActionEventHandler", userActionHandler.Handle),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to register event handlers: %w", err)
	}

	return cqrsService, nil
}

// SendCommand sends a command via the command bus
func (c *CQRSService) SendCommand(ctx context.Context, command interface{}) error {
	return c.commandBus.Send(ctx, command)
}

// PublishEvent publishes an event via the event bus
func (c *CQRSService) PublishEvent(ctx context.Context, event interface{}) error {
	return c.eventBus.Publish(ctx, event)
}

// SetupEventSubscriptions sets up additional event subscriptions for logging/monitoring
func (c *CQRSService) SetupEventSubscriptions() {
	// Add a general event logger handler
	c.router.AddNoPublisherHandler(
		"event_logger",
		"events.*",
		c.subscriber,
		c.logEvent,
	)
}

// logEvent logs all events for monitoring purposes
func (c *CQRSService) logEvent(msg *message.Message) error {
	log.Printf("Event received: ID=%s, Topic=%s", msg.UUID, msg.Metadata.Get("topic"))
	return nil
}

// Publisher interface implementation for service.Publisher
type CQRSPublisher struct {
	cqrsService *CQRSService
}

// NewCQRSPublisher creates a new CQRS publisher that implements service.Publisher
func NewCQRSPublisher(cqrsService *CQRSService) service.Publisher {
	return &CQRSPublisher{
		cqrsService: cqrsService,
	}
}

// PublishMessage publishes a message by creating and sending a command
func (p *CQRSPublisher) PublishMessage(topic string, payload []byte) error {
	command := NewPublishMessageCommand(topic, string(payload), nil)
	return p.cqrsService.SendCommand(context.Background(), command)
}
