package cqrs

import (
	"context"
	"log"

	"github.com/ThreeDotsLabs/watermill/components/cqrs"
	"github.com/google/uuid"
)

// PublishMessageCommand represents a command to publish a message
// In Watermill CQRS, commands are simple structs that don't need to implement interfaces
type PublishMessageCommand struct {
	ID      string                 `json:"id"`
	Topic   string                 `json:"topic"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data,omitempty"`
}

// NewPublishMessageCommand creates a new publish message command
func NewPublishMessageCommand(topic, message string, data map[string]interface{}) *PublishMessageCommand {
	return &PublishMessageCommand{
		ID:      uuid.New().String(),
		Topic:   topic,
		Message: message,
		Data:    data,
	}
}

// PublishMessageCommandHandler handles publish message commands
// In Watermill CQRS, command handlers are simple functions that take the command as a parameter
type PublishMessageCommandHandler struct {
	eventBus *cqrs.EventBus
}

// NewPublishMessageCommandHandler creates a new publish message command handler
func NewPublishMessageCommandHandler(eventBus *cqrs.EventBus) *PublishMessageCommandHandler {
	return &PublishMessageCommandHandler{
		eventBus: eventBus,
	}
}

// Handle handles the publish message command
// This function signature matches what Watermill CQRS expects for command handlers
func (h *PublishMessageCommandHandler) Handle(ctx context.Context, cmd *PublishMessageCommand) error {
	log.Printf("Handling publish message command: topic=%s, message=%s", cmd.Topic, cmd.Message)

	// Create and publish the event
	event := NewMessagePublishedEvent(cmd.Topic, cmd.Message)
	if err := h.eventBus.Publish(ctx, event); err != nil {
		return err
	}

	log.Printf("Successfully published message event: %s", event.ID)
	return nil
}

// Note: CommandBus is now provided by Watermill CQRS component
// We no longer need custom CommandBus implementation as Watermill provides:
// - cqrs.CommandBus for sending commands
// - cqrs.CommandProcessor for handling commands
// - Built-in marshaling and routing capabilities
