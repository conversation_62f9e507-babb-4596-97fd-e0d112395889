package cqrs

import (
	"context"
	"log"
)

// MessagePublishedEventHandler handles message published events
// In Watermill CQRS, event handlers are simple functions that take the event as a parameter
type MessagePublishedEventHandler struct{}

// NewMessagePublishedEventHandler creates a new message published event handler
func NewMessagePublishedEventHandler() *MessagePublishedEventHandler {
	return &MessagePublishedEventHandler{}
}

// <PERSON>le handles the message published event
// This function signature matches what Watermill CQRS expects for event handlers
func (h *MessagePublishedEventHandler) Handle(ctx context.Context, event *MessagePublishedEvent) error {
	log.Printf("Handling message published event: ID=%s, Topic=%s, Message=%s",
		event.ID, event.Topic, event.Message)

	// Here you could implement business logic like:
	// - Sending notifications
	// - Updating read models
	// - Triggering other processes
	// - Logging for analytics

	// For demonstration, we'll just log the event
	log.Printf("Message published successfully: %s on topic %s at %s",
		event.Message, event.Topic, event.Timestamp.Format("2006-01-02 15:04:05"))

	return nil
}

// UserActionEventHandler handles user action events
type UserActionEventHandler struct{}

// NewUserActionEventHandler creates a new user action event handler
func NewUserActionEventHandler() *UserActionEventHandler {
	return &UserActionEventHandler{}
}

// Handle handles the user action event
// This function signature matches what Watermill CQRS expects for event handlers
func (h *UserActionEventHandler) Handle(ctx context.Context, event *UserActionEvent) error {
	log.Printf("Handling user action event: ID=%s, UserID=%s, Action=%s",
		event.ID, event.UserID, event.Action)

	// Here you could implement business logic like:
	// - Updating user statistics
	// - Triggering workflows
	// - Sending notifications
	// - Updating read models

	log.Printf("User %s performed action %s at %s",
		event.UserID, event.Action, event.Timestamp.Format("2006-01-02 15:04:05"))

	return nil
}

// Note: EventBus is now provided by Watermill CQRS component
// We no longer need custom EventBus implementation as Watermill provides:
// - cqrs.EventBus for publishing events
// - cqrs.EventProcessor for handling events
// - cqrs.EventGroupProcessor for handling multiple event types with shared subscriber
// - Built-in marshaling and routing capabilities
