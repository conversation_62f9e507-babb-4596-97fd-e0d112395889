package cqrs

import (
	"time"

	"github.com/google/uuid"
)

// MessagePublishedEvent represents a message published event
// In Watermill CQRS, events are simple structs that don't need to implement interfaces
type MessagePublishedEvent struct {
	ID        string    `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	Topic     string    `json:"topic"`
	Message   string    `json:"message"`
}

// NewMessagePublishedEvent creates a new message published event
func NewMessagePublishedEvent(topic, message string) *MessagePublishedEvent {
	return &MessagePublishedEvent{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Topic:     topic,
		Message:   message,
	}
}

// UserActionEvent represents a user action event
type UserActionEvent struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id"`
	Action    string                 `json:"action"`
	Data      map[string]interface{} `json:"data"`
}

// NewUserActionEvent creates a new user action event
func NewUserActionEvent(userID, action string, data map[string]interface{}) *UserActionEvent {
	return &UserActionEvent{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		UserID:    userID,
		Action:    action,
		Data:      data,
	}
}

// Note: Event serialization/deserialization is now handled by Watermill CQRS marshalers
// We no longer need custom serialization logic as Watermill provides:
// - cqrs.JSONMarshaler for JSON marshaling
// - cqrs.ProtoMarshaler for Protocol Buffers marshaling
// - Custom marshalers can be implemented using cqrs.CommandEventMarshaler interface
