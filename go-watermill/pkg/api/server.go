package api

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go-watermill/internal/config"
	"go-watermill/internal/service"

	"github.com/gin-gonic/gin"
)

// HTTPServer implements the Service interface for HTTP server
type HTTPServer struct {
	config    *config.Config
	publisher service.Publisher
	router    *gin.Engine
	server    *http.Server
	running   chan struct{}
	isRunning bool
	mu        sync.RWMutex
}

// NewHTTPServer creates a new HTTP server
func NewHTTPServer(cfg *config.Config, publisher service.Publisher) service.Service {
	if !cfg.App.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	return &HTTPServer{
		config:    cfg,
		publisher: publisher,
		router:    gin.Default(),
		running:   make(chan struct{}),
		isRunning: false,
	}
}

// Start starts the HTTP server
func (h *HTTPServer) Start(ctx context.Context) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.isRunning {
		return fmt.Errorf("HTTP server is already running")
	}

	h.setupRoutes()

	addr := fmt.Sprintf(":%d", h.config.HTTP.Port)
	h.server = &http.Server{
		Addr:    addr,
		Handler: h.router,
	}

	// Start server in background
	go func() {
		if err := h.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("HTTP server error: %v\n", err)
		}
	}()

	// Give the server a moment to start
	time.Sleep(100 * time.Millisecond)

	h.isRunning = true
	close(h.running)

	fmt.Printf("HTTP server started on port %d\n", h.config.HTTP.Port)
	return nil
}

// Stop implements graceful shutdown of the HTTP server
func (h *HTTPServer) Stop(ctx context.Context) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	if !h.isRunning {
		return nil
	}

	// Create a context with timeout for graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	if err := h.server.Shutdown(shutdownCtx); err != nil {
		return fmt.Errorf("HTTP server shutdown error: %w", err)
	}

	h.isRunning = false
	fmt.Println("HTTP server stopped")
	return nil
}

// Running returns a channel that is closed when the service is ready
func (h *HTTPServer) Running() chan struct{} {
	return h.running
}

// IsRunning returns true if the service is currently running
func (h *HTTPServer) IsRunning() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.isRunning
}

// setupRoutes configures the HTTP routes
func (h *HTTPServer) setupRoutes() {
	// Health check endpoint
	h.router.GET("/health", h.healthHandler)

	// API endpoints
	api := h.router.Group("/api/v1")
	{
		api.POST("/publish", h.publishHandler)
		api.POST("/events", h.eventHandler)
	}

	// Info endpoint
	h.router.GET("/", h.infoHandler)
}

// MessageRequest represents a message publish request
type MessageRequest struct {
	Topic   string                 `json:"topic" binding:"required"`
	Message string                 `json:"message" binding:"required"`
	Data    map[string]interface{} `json:"data,omitempty"`
}

// MessageResponse represents a message publish response
type MessageResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status    string          `json:"status"`
	Timestamp string          `json:"timestamp"`
	Version   string          `json:"version"`
	Services  map[string]bool `json:"services"`
}

// healthHandler handles health check requests
func (h *HTTPServer) healthHandler(c *gin.Context) {
	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   h.config.App.Version,
		Services: map[string]bool{
			"http": h.IsRunning(),
		},
	}

	c.JSON(http.StatusOK, response)
}

// publishHandler handles message publish requests
func (h *HTTPServer) publishHandler(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Publish message via Watermill
	err := h.publisher.PublishMessage(req.Topic, []byte(req.Message))
	if err != nil {
		c.JSON(http.StatusInternalServerError, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{
		Success:   true,
		MessageID: fmt.Sprintf("%d", time.Now().UnixNano()),
	})
}

// eventHandler handles event publish requests (will be used with CQRS)
func (h *HTTPServer) eventHandler(c *gin.Context) {
	var req MessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Publish event via Watermill (this will trigger CQRS handlers)
	err := h.publisher.PublishMessage("events.published", []byte(req.Message))
	if err != nil {
		c.JSON(http.StatusInternalServerError, MessageResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{
		Success:   true,
		MessageID: fmt.Sprintf("event-%d", time.Now().UnixNano()),
	})
}

// infoHandler provides API information
func (h *HTTPServer) infoHandler(c *gin.Context) {
	info := map[string]interface{}{
		"app":     h.config.App.Name,
		"version": h.config.App.Version,
		"endpoints": map[string]string{
			"health":  "/health",
			"publish": "POST /api/v1/publish",
			"events":  "POST /api/v1/events",
		},
	}

	c.JSON(http.StatusOK, info)
}
