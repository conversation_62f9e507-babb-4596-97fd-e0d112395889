version: '3'

vars:
  APP_NAME: go-watermill
  BINARY_NAME: go-watermill
  DOCKER_IMAGE: go-watermill:latest

tasks:
  default:
    desc: Show available tasks
    cmds:
      - task --list

  # Development tasks
  dev:
    desc: Run the application in development mode
    env:
      DEBUG: true
      HTTP_PORT: 8080
    cmds:
      - go run ./cmd/server

  build:
    desc: Build the application binary
    cmds:
      - go build -o bin/{{.BINARY_NAME}} ./cmd/server
    generates:
      - bin/{{.BINARY_NAME}}

  clean:
    desc: Clean build artifacts
    cmds:
      - rm -rf bin/
      - go clean

  # Testing tasks
  test:
    desc: Run all tests
    cmds:
      - go test -v ./...

  test-coverage:
    desc: Run tests with coverage
    cmds:
      - go test -v -coverprofile=coverage.out ./...
      - go tool cover -html=coverage.out -o coverage.html

  # Linting and formatting
  fmt:
    desc: Format Go code
    cmds:
      - go fmt ./...

  lint:
    desc: Run golangci-lint
    cmds:
      - golangci-lint run

  vet:
    desc: Run go vet
    cmds:
      - go vet ./...

  # Dependency management
  deps:
    desc: Download and tidy dependencies
    cmds:
      - go mod download
      - go mod tidy

  deps-update:
    desc: Update dependencies
    cmds:
      - go get -u ./...
      - go mod tidy

  # Docker tasks
  docker-build:
    desc: Build Docker image
    cmds:
      - docker build -t {{.DOCKER_IMAGE}} .

  docker-run:
    desc: Run application in Docker container
    deps: [docker-build]
    cmds:
      - docker run --rm -p 8080:8080 {{.DOCKER_IMAGE}}

  docker-compose-up:
    desc: Start services with docker-compose
    cmds:
      - docker-compose up --build

  docker-compose-down:
    desc: Stop services with docker-compose
    cmds:
      - docker-compose down

  docker-compose-logs:
    desc: View docker-compose logs
    cmds:
      - docker-compose logs -f

  # API testing tasks
  test-api:
    desc: Test API endpoints
    cmds:
      - |
        echo "Testing health endpoint..."
        curl -s http://localhost:8080/health | jq .
        echo ""
        echo "Testing publish endpoint..."
        curl -s -X POST http://localhost:8080/api/v1/publish \
          -H "Content-Type: application/json" \
          -d '{"topic": "test.topic", "message": "Hello World"}' | jq .
        echo ""
        echo "Testing events endpoint..."
        curl -s -X POST http://localhost:8080/api/v1/events \
          -H "Content-Type: application/json" \
          -d '{"topic": "user.action", "message": "User clicked button"}' | jq .

  # Load testing
  load-test:
    desc: Run load test against the API
    cmds:
      - |
        echo "Running load test..."
        for i in {1..10}; do
          curl -s -X POST http://localhost:8080/api/v1/publish \
            -H "Content-Type: application/json" \
            -d "{\"topic\": \"load.test\", \"message\": \"Load test message $i\"}" &
        done
        wait
        echo "Load test completed"

  # Monitoring tasks
  logs:
    desc: Show application logs (when running with docker-compose)
    cmds:
      - docker-compose logs -f app

  health:
    desc: Check application health
    cmds:
      - curl -s http://localhost:8080/health | jq .

  # Setup tasks
  setup:
    desc: Setup development environment
    cmds:
      - task: deps
      - task: fmt
      - task: vet
      - task: test

  # CI/CD tasks
  ci:
    desc: Run CI pipeline tasks
    cmds:
      - task: deps
      - task: fmt
      - task: vet
      - task: lint
      - task: test
      - task: build

  # Release tasks
  release:
    desc: Build release binary
    cmds:
      - CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o bin/{{.BINARY_NAME}}-linux-amd64 ./cmd/server
      - CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags="-w -s" -o bin/{{.BINARY_NAME}}-darwin-amd64 ./cmd/server
      - CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags="-w -s" -o bin/{{.BINARY_NAME}}-windows-amd64.exe ./cmd/server
