# Go Watermill Template

A Golang project template using <PERSON><PERSON> with Go Channel PubSub, implementing CQRS pattern and Service interface for graceful service management.

## Features

- **Service Interface**: All services implement a common interface with graceful shutdown and readiness signaling
- **Watermill Integration**: Uses Watermill with Go Channel as the default PubSub implementation
- **CQ<PERSON> Pattern**: Implements Command Query Responsibility Segregation for event handling
- **HTTP API**: RESTful API with publish endpoints that trigger events
- **Docker Support**: Complete Docker and Docker Compose configuration
- **Task Management**: Taskfile for common development tasks

## Architecture

### Service Interface

All services implement the following interface:

```go
type Service interface {
    Start(ctx context.Context) error
    Stop(ctx context.Context) error  // Graceful shutdown
    Running() chan struct{}          // Ready signal
    IsRunning() bool
}
```

### Components

- **HTTP Server**: Implements Service interface, provides REST API
- **Watermill Service**: Implements Service interface, manages message routing
- **CQRS Service**: Handles commands and events using Watermill
- **Publisher Interface**: Abstraction for message publishing

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /` - API information
- `POST /api/v1/publish` - Publish messages directly
- `POST /api/v1/events` - Publish events through CQRS

### Example Requests

```bash
# Health check
curl http://localhost:8080/health

# Publish a message
curl -X POST http://localhost:8080/api/v1/publish \
  -H "Content-Type: application/json" \
  -d '{"topic": "test.topic", "message": "Hello World"}'

# Publish an event (triggers CQRS)
curl -X POST http://localhost:8080/api/v1/events \
  -H "Content-Type: application/json" \
  -d '{"topic": "user.action", "message": "User clicked button"}'
```

## Development

### Prerequisites

- Go 1.21+
- Task (taskfile.dev)
- Docker & Docker Compose (optional)

### Quick Start

```bash
# Setup development environment
task setup

# Run in development mode
task dev

# Or run with Docker Compose
task docker-compose-up
```

### Available Tasks

```bash
# Show all available tasks
task

# Development
task dev              # Run in development mode
task build            # Build binary
task clean            # Clean build artifacts

# Testing
task test             # Run tests
task test-coverage    # Run tests with coverage
task test-api         # Test API endpoints

# Code Quality
task fmt              # Format code
task lint             # Run linter
task vet              # Run go vet

# Docker
task docker-build     # Build Docker image
task docker-run       # Run in Docker
task docker-compose-up # Start with docker-compose

# Dependencies
task deps             # Download dependencies
task deps-update      # Update dependencies
```

## Configuration

Configuration is handled through environment variables:

- `APP_NAME` - Application name (default: "go-watermill")
- `APP_VERSION` - Application version (default: "1.0.0")
- `DEBUG` - Enable debug mode (default: false)
- `HTTP_PORT` - HTTP server port (default: 8080)
- `WATERMILL_ASYNC` - Enable async publishing (default: true)
- `WATERMILL_MAX_PENDING` - Max pending messages (default: 1000)
- `WATERMILL_CONCURRENT_HANDLERS` - Concurrent handlers (default: 10)
- `WATERMILL_ACK_WAIT` - Acknowledgment wait time (default: 30s)

## Project Structure

```
go-watermill/
├── cmd/server/          # Application entry point
├── internal/
│   ├── config/          # Configuration management
│   ├── service/         # Service interface definition
│   ├── watermill/       # Watermill service implementation
│   └── cqrs/           # CQRS implementation
├── pkg/api/            # HTTP API implementation
├── docker-compose.yml  # Docker Compose configuration
├── Dockerfile         # Docker image definition
├── Taskfile.yml       # Task definitions
└── README.md          # This file
```

## CQRS Implementation

The project implements CQRS pattern with:

- **Commands**: Actions that change state
- **Events**: Things that have happened
- **Handlers**: Process commands and events
- **Command Bus**: Routes commands to handlers
- **Event Bus**: Routes events to handlers

### Example Usage

```go
// Create a command
command := cqrs.NewPublishMessageCommand("topic", "message", nil)

// Handle the command (this will publish an event)
err := cqrsService.HandleCommand(ctx, command)

// Events are automatically handled by registered event handlers
```

## Docker Support

### Single Instance

```bash
docker build -t go-watermill .
docker run -p 8080:8080 go-watermill
```

### Multiple Instances

```bash
docker-compose up --build
```

This starts two instances:
- Instance 1: http://localhost:8080
- Instance 2: http://localhost:8081

## Testing

```bash
# Run all tests
task test

# Test API endpoints (requires running server)
task test-api

# Load testing
task load-test
```

## License

MIT License
